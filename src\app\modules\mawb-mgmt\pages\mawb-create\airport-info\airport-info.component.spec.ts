import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AirportInfoComponent } from './airport-info.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { FormGroup, Validators } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';

describe('AirportInfoComponent', () => {
	let component: AirportInfoComponent;
	let fixture: ComponentFixture<AirportInfoComponent>;
	const mockCurrencies = ['USD', 'EUR', 'GBP'];

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [AirportInfoComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(AirportInfoComponent);
		component = fixture.componentInstance;

		component.currencies = mockCurrencies;

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with correct structure and validators', () => {
		const form = component.airportInfoForm;

		// Test required fields
		expect(form.get('departureAndRequestedRouting')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('airportOfDestination')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('wtOrVal')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('other')?.hasValidator(Validators.required)).toBeTruthy();

		// Test optional fields
		expect(form.get('flight')?.hasValidator(Validators.required)).toBeFalsy();
		expect(form.get('to')?.hasValidator(Validators.required)).toBeFalsy();

		// Test nested form groups
		expect(form.get('amountOfInsurance')).toBeInstanceOf(FormGroup);
		expect(form.get('declaredValueForCarriage')).toBeInstanceOf(FormGroup);
		expect(form.get('declaredValueForCustoms')).toBeInstanceOf(FormGroup);
	});

	it('should pass currencies to child currency components', () => {
		const currencyComponents = fixture.debugElement.queryAll(By.directive(CurrencyInputComponent));

		expect(currencyComponents.length).toBe(3); // amountOfInsurance + declaredValueForCarriage + declaredValueForCustoms

		currencyComponents.forEach((comp) => {
			expect(comp.componentInstance.currencies).toEqual(mockCurrencies);
		});
	});

	it('should validate declaredValueForCarriage numericalValue pattern', () => {
		const control = component.airportInfoForm.get('declaredValueForCarriage.numericalValue');

		// Valid values
		control?.setValue('NCV');
		expect(control?.valid).toBeTruthy();

		control?.setValue('100');
		expect(control?.valid).toBeTruthy();

		control?.setValue('123.45');
		expect(control?.valid).toBeTruthy();

		// Invalid values
		control?.setValue('INVALID');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('123.456');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('123.');
		expect(control?.invalid).toBeTruthy();
	});

	it('should validate declaredValueForCustoms numericalValue pattern', () => {
		const control = component.airportInfoForm.get('declaredValueForCustoms.numericalValue');

		// Valid values
		control?.setValue('NVD');
		expect(control?.valid).toBeTruthy();

		control?.setValue('200');
		expect(control?.valid).toBeTruthy();

		control?.setValue('456.78');
		expect(control?.valid).toBeTruthy();

		// Invalid values
		control?.setValue('INVALID');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('456.789');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('456.');
		expect(control?.invalid).toBeTruthy();
	});

	it('should handle currency input changes', () => {
		const currencyComponents = fixture.debugElement.queryAll(By.directive(CurrencyInputComponent));

		// Test amountOfInsurance
		currencyComponents[0].componentInstance.currencyForm.patchValue({
			currencyUnit: 'EUR',
			numericalValue: '1000',
		});
		fixture.detectChanges();

		expect(component.airportInfoForm.get('amountOfInsurance.currencyUnit')?.value).toBe('EUR');
		expect(component.airportInfoForm.get('amountOfInsurance.numericalValue')?.value).toBe('1000');

		// Test declaredValueForCarriage
		currencyComponents[1].componentInstance.currencyForm.patchValue({
			currencyUnit: 'USD',
			numericalValue: 'NCV',
		});
		fixture.detectChanges();

		expect(component.airportInfoForm.get('declaredValueForCarriage.currencyUnit')?.value).toBe('USD');
		expect(component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.value).toBe('NCV');
	});

	it('should maintain form state when rebuilding', () => {
		// Set form values
		component.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Test Route',
			airportOfDestination: 'Test Destination',
			flight: 'FL123',
			declaredValueForCarriage: {
				currencyUnit: 'GBP',
				numericalValue: 'NCV',
			},
		});

		// Rebuild component
		fixture = TestBed.createComponent(AirportInfoComponent);
		const newComponent = fixture.componentInstance;
		newComponent.currencies = mockCurrencies;
		fixture.detectChanges();

		// Reapply form values
		newComponent.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Test Route',
			airportOfDestination: 'Test Destination',
			flight: 'FL123',
			declaredValueForCarriage: {
				currencyUnit: 'GBP',
				numericalValue: 'NCV',
			},
		});

		fixture.detectChanges();

		// Verify state
		expect(newComponent.airportInfoForm.get('departureAndRequestedRouting')?.value).toBe('Test Route');
		expect(newComponent.airportInfoForm.get('declaredValueForCarriage.currencyUnit')?.value).toBe('GBP');
	});

	it('should handle special values NCV and NVD correctly', () => {
		// Set NCV for declaredValueForCarriage
		component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.setValue('NCV');
		expect(component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.valid).toBeTruthy();

		// Set NVD for declaredValueForCustoms
		component.airportInfoForm.get('declaredValueForCustoms.numericalValue')?.setValue('NVD');
		expect(component.airportInfoForm.get('declaredValueForCustoms.numericalValue')?.valid).toBeTruthy();

		// Set invalid special value
		component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.setValue('INVALID');
		expect(component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.invalid).toBeTruthy();
	});

	it('should validate all required fields as a group', () => {
		// Initially should be invalid
		expect(component.airportInfoForm.invalid).toBeTruthy();

		// Set required fields
		component.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Route',
			airportOfDestination: 'Destination',
			wtOrVal: 'Weight',
			other: 'Other',
		});

		expect(component.airportInfoForm.valid).toBeTruthy();
	});

	it('should not require non-mandatory fields', () => {
		// Set only required fields
		component.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Route',
			airportOfDestination: 'Destination',
			wtOrVal: 'Weight',
			other: 'Other',
		});

		// Optional fields should be valid even when empty
		expect(component.airportInfoForm.get('flight')?.valid).toBeTruthy();
		expect(component.airportInfoForm.get('to')?.valid).toBeTruthy();
		expect(component.airportInfoForm.get('amountOfInsurance')?.valid).toBeTruthy();
	});
});
