<div class="orll-carrier-agent">
	<h2 class="mat-display-2 orll-carrier-agent-form__title">{{ 'hawb.carrierAgent.title' | translate }}</h2>
	<form [formGroup]="carrierAgentForm">
		<div class="row">
			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{ 'hawb.carrierAgent.company' | translate }}</mat-label>
				<input matInput formControlName="company">
				@if (carrierAgentForm.get('company')?.hasError('required')) {
					<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.company' | translate } }}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'hawb.carrierAgent.agentIataCode' | translate }}</mat-label>
				<input matInput formControlName="agentIataCode">
				@if (carrierAgentForm.get('agentIataCode')?.hasError('pattern')) {
					<mat-error>{{
							'validators.pattern'|translate:{
								field: 'hawb.carrierAgent.agentIataCode' | translate,
								requiredPattern: carrierAgentForm.get('agentIataCode')?.getError('pattern').requiredPattern
							}
						}}
					</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'mawb.carrierAgent.accountingNoteText' | translate }}</mat-label>
				<input matInput formControlName="accountingNoteText">
			</mat-form-field>
		</div>
		<div class="row">
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{ 'hawb.carrierAgent.country' | translate }}</mat-label>
				<input type="text" matInput
					formControlName="country"
					[matAutocomplete]="autoCountry">
				<mat-autocomplete #autoCountry="matAutocomplete" [displayWith]="displayCountryName"
					(optionSelected)="countryValueChange($event)">
					@for (country of filteredCountries; track country) {
						<mat-option [value]="country.code">{{country.name}}</mat-option>
					}
				</mat-autocomplete>
				@if (carrierAgentForm.get('country')?.hasError('required')) {
					<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.country' | translate } }}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{ 'hawb.carrierAgent.province' | translate }}</mat-label>
				<input type="text" matInput
					formControlName="province"
					[matAutocomplete]="autoRegion">
				<mat-autocomplete #autoRegion="matAutocomplete" [displayWith]="displayProvinceName"
					(optionSelected)="regionValueChange($event)">
					@for (province of filteredProvinces; track province) {
						<mat-option [value]="province.code">{{province.name}}</mat-option>
					}
				</mat-autocomplete>
				@if (carrierAgentForm.get('province')?.hasError('required')) {
					<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.province' | translate } }}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{ 'hawb.carrierAgent.cityName' | translate }}</mat-label>
				<input type="text" matInput
					formControlName="cityCode"
					[matAutocomplete]="autoCity">
				<mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCityName">
					@for (city of filteredCities; track city) {
						<mat-option [value]="city.code">{{city.name}}</mat-option>
					}
				</mat-autocomplete>
				@if (carrierAgentForm.get('cityName')?.hasError('required')) {
					<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.cityName' | translate } }}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'hawb.carrierAgent.textualPostCode' | translate }}</mat-label>
				<input matInput formControlName="textualPostCode">
			</mat-form-field>
		</div>
		<div class="row">
			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{ 'hawb.carrierAgent.address' | translate }}</mat-label>
				<input matInput formControlName="address">
				@if (carrierAgentForm.get('address')?.hasError('required')) {
					<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.address' | translate } }}</mat-error>
				}
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'hawb.carrierAgent.phoneNumber' | translate }}</mat-label>
				<input matInput formControlName="phoneNumber">
			</mat-form-field>
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{ 'hawb.carrierAgent.email' | translate }}</mat-label>
				<input matInput formControlName="email">
				@if (carrierAgentForm.get('email')?.hasError('email')) {
					<mat-error>{{'sli.mgmt.company.pattern.email' | translate}}</mat-error>
				}
			</mat-form-field>
		</div>
	</form>
</div>
