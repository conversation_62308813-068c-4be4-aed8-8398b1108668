import { TestBed } from '@angular/core/testing';
import { MawbCreateRequestService } from './mawb-create-request.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '@environments/environment';
import { of } from 'rxjs';
import { MawbCreateDto } from '../models/mawb-create.model';
import { HawbCreateDto } from '../../hawb-mgmt/models/hawb-create.model';

const baseUrl = environment.baseApi;

describe('MawbCreateRequestService', () => {
	let service: MawbCreateRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get', 'post']);

		TestBed.configureTestingModule({
			providers: [MawbCreateRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(MawbCreateRequestService);
	});

	describe('getHawbDetail', () => {
		it('should call getData with correct endpoint and parameters', () => {
			// Arrange
			const hawbId = 'test-hawb-id-123';
			const mockHawbDetail: HawbCreateDto = {
				id: {
					iri: false,
					namespace: 'test',
					localName: 'test',
					resource: true,
					bnode: false,
					triple: false,
					literal: false,
				},
				orgId: 'org-123',
				waybillPrefix: '123',
				waybillNumber: '********',
				partyList: [],
				accountingInformation: 'test accounting',
				departureLocation: 'NYC',
				arrivalLocation: 'LAX',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				weightValuationIndicator: 'Y',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 750 },
				textualHandlingInstructions: 'Handle with care',
				totalGrossWeight: 100,
				rateClassCode: 'N',
				totalVolumetricWeight: 50,
				rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
				goodsDescriptionForRate: 'Electronics',
				otherChargeList: [],
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'NYC',
				consignorDeclarationSignature: 'John Doe',
				carrierDeclarationSignature: 'Jane Smith',
			};

			// Mock the HTTP GET call
			httpClientSpy.get.and.returnValue(of(mockHawbDetail));

			// Act
			const result = service.getHawbDetail(hawbId);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockHawbDetail);
			});

			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/info`, { params: jasmine.any(Object) });
		});

		it('should handle empty hawbId parameter', () => {
			// Arrange
			const hawbId = '';
			const mockResponse: HawbCreateDto = {} as HawbCreateDto;

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			const result = service.getHawbDetail(hawbId);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});

			expect(httpClientSpy.get).toHaveBeenCalled();
		});

		it('should return Observable<HawbCreateDto>', () => {
			// Arrange
			const hawbId = 'test-id';
			const mockResponse: HawbCreateDto = {} as HawbCreateDto;

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			const result = service.getHawbDetail(hawbId);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(response).toEqual(mockResponse);
			});
		});
	});

	describe('createMawb', () => {
		it('should call postData with correct endpoint and data', () => {
			// Arrange
			const mockMawbData: MawbCreateDto = {
				waybillPrefix: '123',
				waybillNumber: '********',
				houseWaybills: ['HWB001', 'HWB002'],
				partyList: [],
				accountingNoteList: [],
				departureLocation: 'NYC',
				arrivalLocation: 'LAX',
				requestedFlight: 'AA123',
				requestedDate: '2024-01-01',
				toFirst: 'First destination',
				toSecond: 'Second destination',
				toThird: 'Third destination',
				byFirstCarrier: 'AA',
				bySecondCarrier: 'DL',
				byThirdCarrier: 'UA',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				carrierChargeCode: 'CC',
				weightValuationIndicator: 'Y',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 500 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 750 },
				textualHandlingInstructions: 'Handle with care',
				totalGrossWeight: 100,
				serviceCode: 'SC001',
				rateClassCode: 'N',
				totalVolumetricWeight: 50,
				rateCharge: { currencyUnit: 'USD', numericalValue: 200 },
				goodsDescription: 'Electronics',
				otherChargeList: [],
				destinationCurrencyRate: 1.0,
				destinationCharges: { currencyUnit: 'USD', numericalValue: 100 },
				shippingInfo: 'Standard shipping',
				shippingRefNo: 'REF123',
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'NYC',
				consignorDeclarationSignature: 'John Doe',
				carrierDeclarationSignature: 'Jane Smith',
			};
			const mockResponseId = 'mawb-123';

			httpClientSpy.post.and.returnValue(of(mockResponseId));

			// Act
			const result = service.createMawb(mockMawbData);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponseId);
			});

			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, mockMawbData);
		});

		it('should return Observable<string> with created MAWB ID', () => {
			// Arrange
			const mockMawbData: MawbCreateDto = {} as MawbCreateDto;
			const expectedId = 'new-mawb-id-456';

			httpClientSpy.post.and.returnValue(of(expectedId));

			// Act
			const result = service.createMawb(mockMawbData);

			// Assert
			expect(result).toBeDefined();
			result.subscribe((response) => {
				expect(typeof response).toBe('string');
				expect(response).toEqual(expectedId);
			});
		});

		it('should handle minimal MAWB data', () => {
			// Arrange
			const minimalMawbData: MawbCreateDto = {
				waybillPrefix: '123',
				waybillNumber: '********',
				houseWaybills: [],
				partyList: [],
				accountingNoteList: [],
				departureLocation: 'NYC',
				arrivalLocation: 'LAX',
				requestedFlight: '',
				requestedDate: '',
				toFirst: '',
				toSecond: '',
				toThird: '',
				byFirstCarrier: '',
				bySecondCarrier: '',
				byThirdCarrier: '',
				insuredAmount: { currencyUnit: 'USD', numericalValue: null },
				carrierChargeCode: '',
				weightValuationIndicator: '',
				otherChargesIndicator: '',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: null },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: null },
				textualHandlingInstructions: '',
				totalGrossWeight: null,
				serviceCode: null,
				rateClassCode: null,
				totalVolumetricWeight: null,
				rateCharge: { currencyUnit: 'USD', numericalValue: null },
				goodsDescription: '',
				otherChargeList: [],
				destinationCurrencyRate: null,
				destinationCharges: { currencyUnit: 'USD', numericalValue: null },
				shippingInfo: '',
				shippingRefNo: '',
				carrierDeclarationDate: '',
				carrierDeclarationPlace: '',
				consignorDeclarationSignature: '',
				carrierDeclarationSignature: '',
			};
			const mockResponseId = 'minimal-mawb-id';

			httpClientSpy.post.and.returnValue(of(mockResponseId));

			// Act
			const result = service.createMawb(minimalMawbData);

			// Assert
			result.subscribe((response) => {
				expect(response).toEqual(mockResponseId);
			});

			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/mawb-management`, minimalMawbData);
		});
	});
});
