import { TestBed } from '@angular/core/testing';
import { MawbCreateRequestService } from './mawb-create-request.service';
import { HttpClient } from '@angular/common/http';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('MawbCreateRequestService', () => {
	let service: MawbCreateRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);

		TestBed.configureTestingModule({
			providers: [MawbCreateRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(MawbCreateRequestService);
	});
});
